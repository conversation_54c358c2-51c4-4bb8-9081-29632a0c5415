import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

/// Widget that handles keyboard input for crossword game
class KeyboardHandler extends StatefulWidget {
  final Widget child;

  const KeyboardHandler({
    super.key,
    required this.child,
  });

  @override
  State<KeyboardHandler> createState() => _KeyboardHandlerState();
}

class _KeyboardHandlerState extends State<KeyboardHandler> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Only request focus on desktop platforms to avoid virtual keyboard on mobile
    if (Theme.of(context).platform == TargetPlatform.windows ||
        Theme.of(context).platform == TargetPlatform.macOS ||
        Theme.of(context).platform == TargetPlatform.linux) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Only enable autofocus on desktop platforms
    final isDesktop = Theme.of(context).platform == TargetPlatform.windows ||
        Theme.of(context).platform == TargetPlatform.macOS ||
        Theme.of(context).platform == TargetPlatform.linux;

    return Focus(
      focusNode: _focusNode,
      autofocus: isDesktop,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () {
          // Only request focus on desktop platforms
          if (isDesktop) {
            _focusNode.requestFocus();
          }
        },
        behavior: HitTestBehavior.translucent, // Allow taps to pass through
        child: widget.child,
      ),
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    final gameProvider = context.read<CrosswordGameProvider>();

    // Handle different key types
    if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
      gameProvider.moveUp();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
      gameProvider.moveDown();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft) {
      gameProvider.moveLeft();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.arrowRight) {
      gameProvider.moveRight();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.backspace) {
      gameProvider.deleteCharacter();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.delete) {
      gameProvider.clearCurrentCell();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.tab) {
      if (HardwareKeyboard.instance.isShiftPressed) {
        // Shift+Tab: Move to previous word
        gameProvider.moveToPreviousWord();
      } else {
        // Tab: Move to next word
        gameProvider.moveToNextWord();
      }
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.space) {
      gameProvider.toggleDirection();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.enter) {
      gameProvider.toggleDirection();
      return KeyEventResult.handled;
    } else if (event.logicalKey == LogicalKeyboardKey.escape) {
      // Clear selection or pause game
      gameProvider.pauseGame();
      return KeyEventResult.handled;
    }

    // Handle character input
    final character = event.character;
    if (character != null && character.length == 1) {
      final isLetter = RegExp(r'^[a-zA-Z]$').hasMatch(character);
      if (isLetter) {
        gameProvider.inputCharacter(character);
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }
}

/// Extension methods for CrosswordGameProvider to handle word navigation
extension WordNavigation on CrosswordGameProvider {
  void moveToNextWord() {
    if (selectedCell == null || crossword == null) return;

    // Find the next word in the same direction
    final currentDirection = selectedCell!.direction;
    final wordPositions = crossword!.wordPositions
        .where((wp) =>
            currentDirection == Direction.across ? wp.isAcross : wp.isDown)
        .toList();

    // Sort by position
    wordPositions.sort((a, b) {
      if (currentDirection == Direction.across) {
        final rowCompare = a.row.compareTo(b.row);
        return rowCompare != 0 ? rowCompare : a.col.compareTo(b.col);
      } else {
        final colCompare = a.col.compareTo(b.col);
        return colCompare != 0 ? colCompare : a.row.compareTo(b.row);
      }
    });

    // Find current word index
    int currentIndex = -1;
    for (int i = 0; i < wordPositions.length; i++) {
      final wp = wordPositions[i];
      if (currentDirection == Direction.across) {
        if (selectedCell!.row == wp.row &&
            selectedCell!.col >= wp.col &&
            selectedCell!.col < wp.col + wp.word.length) {
          currentIndex = i;
          break;
        }
      } else {
        if (selectedCell!.col == wp.col &&
            selectedCell!.row >= wp.row &&
            selectedCell!.row < wp.row + wp.word.length) {
          currentIndex = i;
          break;
        }
      }
    }

    // Move to next word
    if (currentIndex >= 0 && currentIndex < wordPositions.length - 1) {
      final nextWord = wordPositions[currentIndex + 1];
      selectCell(nextWord.row, nextWord.col,
          preferredDirection: currentDirection);
    } else if (wordPositions.isNotEmpty) {
      // Wrap to first word
      final firstWord = wordPositions[0];
      selectCell(firstWord.row, firstWord.col,
          preferredDirection: currentDirection);
    }
  }

  void moveToPreviousWord() {
    if (selectedCell == null || crossword == null) return;

    // Find the previous word in the same direction
    final currentDirection = selectedCell!.direction;
    final wordPositions = crossword!.wordPositions
        .where((wp) =>
            currentDirection == Direction.across ? wp.isAcross : wp.isDown)
        .toList();

    // Sort by position
    wordPositions.sort((a, b) {
      if (currentDirection == Direction.across) {
        final rowCompare = a.row.compareTo(b.row);
        return rowCompare != 0 ? rowCompare : a.col.compareTo(b.col);
      } else {
        final colCompare = a.col.compareTo(b.col);
        return colCompare != 0 ? colCompare : a.row.compareTo(b.row);
      }
    });

    // Find current word index
    int currentIndex = -1;
    for (int i = 0; i < wordPositions.length; i++) {
      final wp = wordPositions[i];
      if (currentDirection == Direction.across) {
        if (selectedCell!.row == wp.row &&
            selectedCell!.col >= wp.col &&
            selectedCell!.col < wp.col + wp.word.length) {
          currentIndex = i;
          break;
        }
      } else {
        if (selectedCell!.col == wp.col &&
            selectedCell!.row >= wp.row &&
            selectedCell!.row < wp.row + wp.word.length) {
          currentIndex = i;
          break;
        }
      }
    }

    // Move to previous word
    if (currentIndex > 0) {
      final prevWord = wordPositions[currentIndex - 1];
      selectCell(prevWord.row, prevWord.col,
          preferredDirection: currentDirection);
    } else if (wordPositions.isNotEmpty) {
      // Wrap to last word
      final lastWord = wordPositions[wordPositions.length - 1];
      selectCell(lastWord.row, lastWord.col,
          preferredDirection: currentDirection);
    }
  }
}
