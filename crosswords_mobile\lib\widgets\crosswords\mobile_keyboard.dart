import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../providers/crossword_game_provider.dart';

class MobileKeyboard extends StatelessWidget {
  final bool showKeyboard;
  final VoidCallback? onHide;

  const MobileKeyboard({
    super.key,
    this.showKeyboard = true,
    this.onHide,
  });

  static const List<List<String>> _keyboardLayout = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
  ];

  @override
  Widget build(BuildContext context) {
    if (!showKeyboard) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Keyboard rows
            ..._keyboardLayout.map((row) => _buildKeyboardRow(
                  context,
                  row,
                  gameProvider,
                )),

            // Bottom row with special keys
            _buildBottomRow(context, gameProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyboardRow(
    BuildContext context,
    List<String> keys,
    CrosswordGameProvider gameProvider,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: keys
            .map((key) => _buildKey(
                  context,
                  key,
                  onPressed: () => _onKeyPressed(gameProvider, key),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildBottomRow(
    BuildContext context,
    CrosswordGameProvider gameProvider,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Clear key
          _buildSpecialKey(
            context,
            icon: Icons.clear,
            label: 'Clear',
            onPressed: () => _onClearPressed(gameProvider),
          ),

          const SizedBox(width: 8),

          // Backspace key
          _buildSpecialKey(
            context,
            icon: Icons.backspace,
            label: 'Backspace',
            onPressed: () => _onBackspacePressed(gameProvider),
          ),

          const SizedBox(width: 8),

          // Hide keyboard key
          if (onHide != null)
            _buildSpecialKey(
              context,
              icon: Icons.keyboard_hide,
              label: 'Hide',
              onPressed: onHide!,
            ),
        ],
      ),
    );
  }

  Widget _buildKey(
    BuildContext context,
    String key, {
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(6),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            width: 32,
            height: 40,
            alignment: Alignment.center,
            child: Text(
              key,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialKey(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: theme.colorScheme.primary.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 60,
          height: 40,
          alignment: Alignment.center,
          child: Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  void _onKeyPressed(CrosswordGameProvider gameProvider, String key) {
    HapticFeedback.lightImpact();
    gameProvider.inputCharacter(key);
  }

  void _onBackspacePressed(CrosswordGameProvider gameProvider) {
    HapticFeedback.lightImpact();
    gameProvider.deleteCharacter();
  }

  void _onClearPressed(CrosswordGameProvider gameProvider) {
    HapticFeedback.mediumImpact();
    // Clear current word
    final selectedCell = gameProvider.selectedCell;
    if (selectedCell != null) {
      gameProvider.clearCurrentWord();
    }
  }
}

// Physical keyboard handler
class PhysicalKeyboardHandler extends StatefulWidget {
  final Widget child;

  const PhysicalKeyboardHandler({
    super.key,
    required this.child,
  });

  @override
  State<PhysicalKeyboardHandler> createState() =>
      _PhysicalKeyboardHandlerState();
}

class _PhysicalKeyboardHandlerState extends State<PhysicalKeyboardHandler> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () => _focusNode.requestFocus(),
        behavior: HitTestBehavior.translucent, // Allow taps to pass through
        child: widget.child,
      ),
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    final gameProvider = context.read<CrosswordGameProvider>();

    // Handle letter keys
    if (event.logicalKey.keyLabel.length == 1) {
      final char = event.logicalKey.keyLabel.toUpperCase();
      if (RegExp(r'^[A-Z]$').hasMatch(char)) {
        gameProvider.inputCharacter(char);
        return KeyEventResult.handled;
      }
    }

    // Handle special keys
    switch (event.logicalKey) {
      case LogicalKeyboardKey.backspace:
        gameProvider.deleteCharacter();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowLeft:
        _moveSelection(gameProvider, -1, 0);
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowRight:
        _moveSelection(gameProvider, 1, 0);
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowUp:
        _moveSelection(gameProvider, 0, -1);
        return KeyEventResult.handled;

      case LogicalKeyboardKey.arrowDown:
        _moveSelection(gameProvider, 0, 1);
        return KeyEventResult.handled;

      case LogicalKeyboardKey.tab:
        _switchDirection(gameProvider);
        return KeyEventResult.handled;

      case LogicalKeyboardKey.space:
        // Check if shift is pressed using HardwareKeyboard
        final isShiftPressed = HardwareKeyboard.instance.logicalKeysPressed
                .contains(LogicalKeyboardKey.shiftLeft) ||
            HardwareKeyboard.instance.logicalKeysPressed
                .contains(LogicalKeyboardKey.shiftRight);

        if (isShiftPressed) {
          _switchDirection(gameProvider);
        } else {
          gameProvider.inputCharacter(' ');
        }
        return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  void _moveSelection(
      CrosswordGameProvider gameProvider, int deltaCol, int deltaRow) {
    final selectedCell = gameProvider.selectedCell;
    if (selectedCell == null) return;

    final newRow = selectedCell.row + deltaRow;
    final newCol = selectedCell.col + deltaCol;

    gameProvider.selectCell(newRow, newCol);
  }

  void _switchDirection(CrosswordGameProvider gameProvider) {
    final selectedCell = gameProvider.selectedCell;
    if (selectedCell == null) return;

    final newDirection = selectedCell.direction == Direction.across
        ? Direction.down
        : Direction.across;

    gameProvider.selectCell(
      selectedCell.row,
      selectedCell.col,
      preferredDirection: newDirection,
    );
  }
}

// Adaptive keyboard that shows/hides based on platform
class AdaptiveKeyboard extends StatelessWidget {
  final bool forceShow;

  const AdaptiveKeyboard({
    super.key,
    this.forceShow = false,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isPhysicalKeyboard = mediaQuery.viewInsets.bottom == 0;

    // Show virtual keyboard on mobile when no physical keyboard is detected
    final showVirtualKeyboard = forceShow ||
        (Theme.of(context).platform == TargetPlatform.android ||
                Theme.of(context).platform == TargetPlatform.iOS) &&
            isPhysicalKeyboard;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: showVirtualKeyboard
          ? const MobileKeyboard()
          : const SizedBox.shrink(),
    );
  }
}
