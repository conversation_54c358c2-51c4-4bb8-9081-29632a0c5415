import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/models/crossword_models.dart';
import '../../providers/crossword_game_provider.dart';

class CrosswordCell extends StatelessWidget {
  final int row;
  final int col;
  final GridCell gridCell;
  final String userInput;
  final double cellSize;
  final VoidCallback? onTap;

  const CrosswordCell({
    super.key,
    required this.row,
    required this.col,
    required this.gridCell,
    required this.userInput,
    required this.cellSize,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gameProvider = context.watch<CrosswordGameProvider>();

    // Determine cell state
    final isSelected = gameProvider.selectedCell?.row == row &&
        gameProvider.selectedCell?.col == col;
    final isHighlighted = gameProvider.highlightedCells
        .any((cell) => cell.row == row && cell.col == col);
    final isEmpty = gridCell.isEmpty;
    final isBlack = gridCell.isBlack;
    final hasUserInput = userInput.isNotEmpty;
    final isCorrect =
        hasUserInput && userInput.toUpperCase() == gridCell.char.toUpperCase();
    final isError = hasUserInput && !isCorrect && gameProvider.showErrors;

    // Get word number for this cell (if it's the start of a word)
    final wordNumber = _getWordNumber(gameProvider);

    return GestureDetector(
      onTap: () {
        // Debug logging
        debugPrint(
            'Cell tapped: ($row, $col) - isEmpty: $isEmpty, isBlack: $isBlack');
        onTap?.call();
      },
      behavior:
          HitTestBehavior.opaque, // Ensure the entire cell area is tappable
      child: Container(
        width: cellSize,
        height: cellSize,
        decoration: BoxDecoration(
          color: _getCellColor(
              theme, isSelected, isHighlighted, isEmpty, isBlack, isError),
          border: Border.all(
            color: _getBorderColor(theme, isSelected, isHighlighted),
            width: _getBorderWidth(isSelected),
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: isBlack
            ? null
            : Stack(
                children: [
                  // Word number (top-left corner)
                  if (wordNumber != null)
                    Positioned(
                      top: 2,
                      left: 2,
                      child: Text(
                        wordNumber.toString(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontSize: cellSize * 0.2,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ),

                  // User input character (center)
                  Center(
                    child: Text(
                      userInput.toUpperCase(),
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontSize: cellSize * 0.5,
                        fontWeight: FontWeight.bold,
                        color: isError
                            ? theme.colorScheme.error
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                  ),

                  // Correct answer hint (for debugging - remove in production)
                  if (gameProvider.gameState == GameState.completed &&
                      !hasUserInput)
                    Center(
                      child: Text(
                        gridCell.char.toUpperCase(),
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontSize: cellSize * 0.5,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                ],
              ),
      ),
    );
  }

  // Get cell background color based on state
  Color _getCellColor(
    ThemeData theme,
    bool isSelected,
    bool isHighlighted,
    bool isEmpty,
    bool isBlack,
    bool isError,
  ) {
    // Black cells (non-playable) should have dark background
    if (isBlack) {
      return Colors.black87;
    }

    // Empty cells (non-playable) should also have dark background
    if (isEmpty) {
      return Colors.black87;
    }

    // Error state for incorrect input
    if (isError) {
      return theme.colorScheme.error.withValues(alpha: 0.2);
    }

    // Selected cell highlighting
    if (isSelected) {
      return theme.colorScheme.primary.withValues(alpha: 0.4);
    }

    // Word highlighting
    if (isHighlighted) {
      return theme.colorScheme.primary.withValues(alpha: 0.15);
    }

    // Default playable cell (white background)
    return Colors.white;
  }

  // Get cell border color based on state
  Color _getBorderColor(ThemeData theme, bool isSelected, bool isHighlighted) {
    if (isSelected) {
      return theme.colorScheme.primary;
    }

    if (isHighlighted) {
      return theme.colorScheme.primary.withValues(alpha: 0.6);
    }

    return Colors.grey.shade400;
  }

  // Get border width based on selection state
  double _getBorderWidth(bool isSelected) {
    return isSelected ? 3.0 : 1.0;
  }

  // Get word number for this cell if it's the start of a word
  int? _getWordNumber(CrosswordGameProvider gameProvider) {
    final crossword = gameProvider.crossword;
    if (crossword == null) return null;

    // Check if this cell is the start of any word
    for (final wordPos in crossword.wordPositions) {
      if (wordPos.row == row && wordPos.col == col) {
        return wordPos.number;
      }
    }

    return null;
  }
}

// Animated version of CrosswordCell for smooth transitions
class AnimatedCrosswordCell extends StatefulWidget {
  final int row;
  final int col;
  final GridCell gridCell;
  final String userInput;
  final double cellSize;
  final VoidCallback? onTap;
  final Duration animationDuration;

  const AnimatedCrosswordCell({
    super.key,
    required this.row,
    required this.col,
    required this.gridCell,
    required this.userInput,
    required this.cellSize,
    this.onTap,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<AnimatedCrosswordCell> createState() => _AnimatedCrosswordCellState();
}

class _AnimatedCrosswordCellState extends State<AnimatedCrosswordCell>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AnimatedCrosswordCell oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Animate when user input changes
    if (oldWidget.userInput != widget.userInput) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: CrosswordCell(
              row: widget.row,
              col: widget.col,
              gridCell: widget.gridCell,
              userInput: widget.userInput,
              cellSize: widget.cellSize,
              onTap: widget.onTap,
            ),
          ),
        );
      },
    );
  }
}
